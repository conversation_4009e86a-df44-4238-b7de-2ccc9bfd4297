import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import '../models/conversation_models.dart';
import '../../core/constants/api_constants.dart';

/// Service for managing ElevenLabs Conversational AI integration
class ElevenLabsConversationalService {
  static ElevenLabsConversationalService? _instance;
  static ElevenLabsConversationalService get instance {
    _instance ??= ElevenLabsConversationalService._();
    return _instance!;
  }
  
  ElevenLabsConversationalService._();
  
  // WebSocket connection
  WebSocketChannel? _webSocketChannel;
  Timer? _keepAliveTimer;
  
  // Audio components
  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  StreamSubscription<Uint8List>? _audioStreamSubscription;
  
  // State management
  bool _isInitialized = false;
  bool _isConnected = false;
  bool _isRecording = false;
  String? _conversationId;
  String? _currentRecordingPath;
  
  // Stream controllers
  final StreamController<ConversationStatus> _statusController = 
      StreamController<ConversationStatus>.broadcast();
  final StreamController<ConversationMode> _modeController = 
      StreamController<ConversationMode>.broadcast();
  final StreamController<String> _transcriptionController = 
      StreamController<String>.broadcast();
  final StreamController<String> _responseController = 
      StreamController<String>.broadcast();
  final StreamController<bool> _recordingStatusController = 
      StreamController<bool>.broadcast();
  final StreamController<double> _vadScoreController = 
      StreamController<double>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  
  // Public streams
  Stream<ConversationStatus> get statusStream => _statusController.stream;
  Stream<ConversationMode> get modeStream => _modeController.stream;
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<String> get responseStream => _responseController.stream;
  Stream<bool> get recordingStatusStream => _recordingStatusController.stream;
  Stream<double> get vadScoreStream => _vadScoreController.stream;
  Stream<String> get errorStream => _errorController.stream;
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isConnected => _isConnected;
  bool get isRecording => _isRecording;
  String? get conversationId => _conversationId;
  
  /// Request microphone permission explicitly
  Future<bool> requestMicrophonePermission() async {
    try {
      var permission = await Permission.microphone.status;

      if (permission.isDenied) {
        permission = await Permission.microphone.request();
      }

      return permission == PermissionStatus.granted;
    } catch (e) {
      debugPrint('❌ Error requesting microphone permission: $e');
      return false;
    }
  }

  /// Initialize the service
  Future<void> initialize() async {
    try {
      _statusController.add(ConversationStatus.connecting);

      // Environment variables should already be loaded in main.dart
      // Check if required environment variables are available
      final agentId = dotenv.env['ELEVENLABS_AGENT_ID'];
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];

      if (agentId == null || agentId.isEmpty) {
        throw Exception('ELEVENLABS_AGENT_ID not found in environment variables');
      }

      if (apiKey == null || apiKey.isEmpty || apiKey == 'your_api_key_here') {
        throw Exception('Please set your ElevenLabs API key in the .env file');
      }

      // Configure audio player for better iOS compatibility
      await _configureAudioPlayer();

      // Check and request microphone permission with better iOS handling
      var micPermission = await Permission.microphone.status;
      debugPrint('🎤 Initial microphone permission status: $micPermission');

      // Handle all non-granted states
      if (micPermission != PermissionStatus.granted) {
        if (micPermission == PermissionStatus.permanentlyDenied || micPermission == PermissionStatus.restricted) {
          debugPrint('⚠️ Microphone permission is permanently denied or restricted');
          throw Exception('Microphone permission denied. Please enable in Settings > Privacy & Security > Microphone');
        }

        // Try to request permission
        debugPrint('📱 Requesting microphone permission...');
        micPermission = await Permission.microphone.request();
        debugPrint('🎤 Permission request result: $micPermission');
      }

      // Final check after request - use explicit comparison
      if (micPermission != PermissionStatus.granted) {
        String errorMessage = 'Microphone permission is required for voice chat';

        if (micPermission == PermissionStatus.permanentlyDenied) {
          errorMessage = 'Microphone permission denied. Please enable in Settings > Privacy & Security > Microphone';
        } else if (micPermission == PermissionStatus.denied) {
          errorMessage = 'Microphone permission was denied. Please try again and allow access when prompted';
        } else if (micPermission == PermissionStatus.restricted) {
          errorMessage = 'Microphone access is restricted on this device';
        }

        throw Exception(errorMessage);
      }

      _isInitialized = true;
      _statusController.add(ConversationStatus.disconnected);

      debugPrint('🎤 Service initialized');
    } catch (e) {
      _statusController.add(ConversationStatus.error);
      _errorController.add('Failed to initialize: $e');
      debugPrint('❌ Failed to initialize ElevenLabs service: $e');
      rethrow;
    }
  }
  
  /// Start a conversation session
  Future<void> startConversation() async {
    if (!_isInitialized) {
      throw Exception('Service not initialized');
    }
    
    if (_isConnected) {
      debugPrint('⚠️ Conversation already active');
      return;
    }
    
    try {
      _statusController.add(ConversationStatus.connecting);
      
      // Get agent ID from environment
      final agentId = dotenv.env['ELEVENLABS_AGENT_ID'];
      if (agentId == null || agentId.isEmpty) {
        throw Exception('Agent ID not configured');
      }
      
      // Create WebSocket connection with authentication
      final apiKey = dotenv.env['ELEVENLABS_API_KEY'];
      
      // Build the WebSocket URL with proper authentication
      final wsUrl = 'wss://api.elevenlabs.io/v1/convai/conversation?agent_id=$agentId&xi-api-key=$apiKey';

      debugPrint('🔗 Connecting to ElevenLabs...');

      _webSocketChannel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to WebSocket messages
      _webSocketChannel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );
      
      // Send conversation initiation message with simplified structure
      final initiationMessage = ConversationInitiationMessage(
        agentId: agentId,
      );

      _sendMessage(initiationMessage);
      
      // Set initial mode
      _modeController.add(ConversationMode.idle);
      
    } catch (e) {
      _statusController.add(ConversationStatus.error);
      _errorController.add('Failed to start conversation: $e');
      debugPrint('❌ Failed to start conversation: $e');
      rethrow;
    }
  }
  
  /// End the conversation session
  Future<void> endConversation() async {
    try {
      _keepAliveTimer?.cancel();
      _keepAliveTimer = null;
      
      if (_isRecording) {
        await stopRecording();
      }
      
      _webSocketChannel?.sink.close();
      _webSocketChannel = null;
      
      _isConnected = false;
      _conversationId = null;
      
      _statusController.add(ConversationStatus.disconnected);
      _modeController.add(ConversationMode.idle);
      
      debugPrint('🔚 Conversation ended');
    } catch (e) {
      debugPrint('❌ Error ending conversation: $e');
    }
  }
  
  /// Start recording audio stream
  Future<void> startRecording() async {
    if (!_isConnected || _isRecording) return;
    
    try {
      final stream = await _audioRecorder.startStream(
        const RecordConfig(
          encoder: AudioEncoder.pcm16bits,
          sampleRate: ApiConstants.sampleRate,
          numChannels: ApiConstants.channels,
        ),
      );
      
      _audioStreamSubscription = stream.listen(
        (chunk) {
          if (_isConnected) {
            final base64Audio = base64Encode(chunk);
            final audioMessage = UserAudioChunkMessage(userAudioChunk: base64Audio);
            _sendMessage(audioMessage);
          }
        },
        onError: (error) {
          debugPrint('❌ Recording stream error: $error');
          _errorController.add('Recording error: $error');
          if (_isRecording) {
            stopRecording();
          }
        },
        onDone: () {
          debugPrint('🎤 Recording stream ended');
          if (_isRecording) {
            stopRecording();
          }
        },
      );
      
      _isRecording = true;
      _recordingStatusController.add(true);
      _modeController.add(ConversationMode.listening);
      
      debugPrint('🎤 Started recording stream');
    } catch (e) {
      _isRecording = false;
      _recordingStatusController.add(false);
      _errorController.add('Failed to start recording: $e');
      debugPrint('❌ Failed to start recording: $e');
      rethrow;
    }
  }
  
  /// Stop recording audio stream
  Future<void> stopRecording() async {
    if (!_isRecording) return;
    
    try {
      await _audioStreamSubscription?.cancel();
      _audioStreamSubscription = null;

      if (await _audioRecorder.isRecording()) {
        await _audioRecorder.stop();
      }
      
      _isRecording = false;
      _recordingStatusController.add(false);
      _modeController.add(ConversationMode.idle);
      
      debugPrint('🎤 Stopped recording stream');
    } catch (e) {
      // Avoid inconsistent state
      _isRecording = false;
      _recordingStatusController.add(false);
      _errorController.add('Failed to stop recording: $e');
      debugPrint('❌ Failed to stop recording: $e');
    }
  }
  
  /// Send a message through WebSocket
  void _sendMessage(ConversationMessage message) {
    if (_webSocketChannel != null) {
      final jsonMessage = jsonEncode(message.toJson());
      _webSocketChannel!.sink.add(jsonMessage);
      // Only log important message types
      if (message.type != 'pong' && message.type != 'user_audio_chunk') {
        debugPrint('📤 Sent: ${message.type}');
      }
    }
  }
  
  /// Handle incoming WebSocket messages
  void _handleWebSocketMessage(dynamic data) {
    try {
      // Handle both String and Map data types
      Map<String, dynamic> jsonData;
      if (data is String) {
        jsonData = jsonDecode(data) as Map<String, dynamic>;
      } else if (data is Map<String, dynamic>) {
        jsonData = data;
      } else {
        debugPrint('❌ Unexpected data type: ${data.runtimeType}');
        return;
      }

      final message = ConversationMessage.fromJson(jsonData);

      // Clean logging - only log message type and key info
      switch (message.type) {
        case 'conversation_initiation_metadata':
          debugPrint('📥 Connected to conversation');
          _handleInitiationMetadata(message as ConversationInitiationMetadata);
          break;
        case 'user_transcript':
          debugPrint('📥 User transcript received');
          _handleUserTranscript(message as UserTranscriptMessage);
          break;
        case 'agent_response':
          debugPrint('📥 Agent response received');
          _handleAgentResponse(message as AgentResponseMessage);
          break;
        case 'audio':
          debugPrint('📥 Audio response received');
          _handleAudioResponse(message as AudioMessage);
          break;
        case 'ping':
          debugPrint('📥 Ping received - sending pong');
          _handlePing(message as PingMessage);
          break;
        case 'vad_score':
          // Skip VAD logging - too verbose
          _handleVadScore(message as VadScoreMessage);
          break;
        case 'interruption':
          debugPrint('📥 Conversation interrupted');
          _handleInterruption(message as InterruptionMessage);
          break;
        case 'agent_response_correction':
          debugPrint('📥 Agent response corrected');
          _handleAgentResponseCorrection(message as AgentResponseCorrectionMessage);
          break;
        default:
          debugPrint('📥 Unknown message: ${message.type}');
          debugPrint('📊 Raw message data: $jsonData');
          // Try to handle ping in raw format as fallback
          if (jsonData['type'] == 'ping') {
            debugPrint('📥 Fallback ping handling');
            _handleRawPing(jsonData);
          }
      }
    } catch (e) {
      debugPrint('❌ Error handling message: $e');
      debugPrint('📊 Raw data: $data');
    }
  }
  
  /// Handle conversation initiation metadata
  void _handleInitiationMetadata(ConversationInitiationMetadata metadata) {
    _conversationId = metadata.conversationId;
    _isConnected = true;
    _statusController.add(ConversationStatus.connected);
    
    // Start recording stream automatically when connected
    startRecording().catchError((error) {
      debugPrint('❌ Failed to start recording: $error');
      _errorController.add('Failed to start recording: $error');
    });
    
    debugPrint('✅ Conversation started: $_conversationId');
  }
  
  /// Handle user transcript
  void _handleUserTranscript(UserTranscriptMessage message) {
    _transcriptionController.add(message.userTranscript);
    debugPrint('👤 User: ${message.userTranscript}');
  }
  
  /// Handle agent response
  void _handleAgentResponse(AgentResponseMessage message) {
    _responseController.add(message.agentResponse);
    _modeController.add(ConversationMode.speaking);
    debugPrint('🤖 Nathan: ${message.agentResponse}');
  }
  
  /// Handle audio response
  void _handleAudioResponse(AudioMessage message) async {
    try {
      // Pause recording stream to prevent echo
      _audioStreamSubscription?.pause();

      // Decode base64 audio and play it
      final audioBytes = base64Decode(message.audioBase64);
      await _playAudioBytes(audioBytes);
    } catch (e) {
      debugPrint('❌ Failed to play audio: $e');
      // Resume stream in case of error
      if (_audioStreamSubscription?.isPaused ?? false) {
        _audioStreamSubscription?.resume();
      }
    }
  }
  
  /// Handle ping message
  void _handlePing(PingMessage message) {
    try {
      // Respond with pong immediately
      final pongMessage = PongMessage(eventId: message.eventId);
      _sendMessage(pongMessage);
      debugPrint('🏓 Pong sent for ping ${message.eventId}');
    } catch (e) {
      debugPrint('❌ Error handling ping: $e');
      // Try raw pong response as fallback
      _sendRawPong();
    }
  }
  
  /// Handle raw ping message as fallback
  void _handleRawPing(Map<String, dynamic> jsonData) {
    try {
      // Extract event ID if available
      final pingEvent = jsonData['ping_event'] as Map<String, dynamic>?;
      final eventId = pingEvent?['event_id'] as int? ?? DateTime.now().millisecondsSinceEpoch;
      
      // Send raw pong response
      final pongResponse = {
        'type': 'pong',
        'pong_event': {
          'event_id': eventId,
        }
      };
      
      _webSocketChannel?.sink.add(jsonEncode(pongResponse));
      debugPrint('🏓 Raw pong sent for event $eventId');
    } catch (e) {
      debugPrint('❌ Error handling raw ping: $e');
      _sendRawPong();
    }
  }
  
  /// Send basic raw pong response
  void _sendRawPong() {
    try {
      final basicPong = {
        'type': 'pong',
        'pong_event': {
          'event_id': DateTime.now().millisecondsSinceEpoch,
        }
      };
      
      _webSocketChannel?.sink.add(jsonEncode(basicPong));
      debugPrint('🏓 Basic pong sent');
    } catch (e) {
      debugPrint('❌ Error sending basic pong: $e');
    }
  }
  
  /// Handle VAD score
  void _handleVadScore(VadScoreMessage message) {
    _vadScoreController.add(message.vadScore);
  }

  /// Handle interruption message
  void _handleInterruption(InterruptionMessage message) {
    debugPrint('🚫 Conversation interrupted');
    _modeController.add(ConversationMode.idle);
  }

  /// Handle agent response correction
  void _handleAgentResponseCorrection(AgentResponseCorrectionMessage message) {
    debugPrint('✏️ Agent response corrected');
    // Update the response stream with the corrected response
    _responseController.add(message.correctedAgentResponse);
  }
  
  /// Play audio bytes
  Future<void> _playAudioBytes(Uint8List audioBytes) async {
    try {
      // Save audio to temporary file with proper WAV format for iOS
      final tempDir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final audioFile = File('${tempDir.path}/response_$timestamp.wav');

      // Convert PCM data to WAV format with proper headers
      final wavBytes = _convertPcmToWav(audioBytes);
      await audioFile.writeAsBytes(wavBytes);

      // Verify file exists and has content
      if (!await audioFile.exists() || await audioFile.length() == 0) {
        debugPrint('❌ Audio file is empty');
        _modeController.add(ConversationMode.idle);
        return;
      }

      debugPrint('🔊 Playing audio (${(wavBytes.length / 1024).toStringAsFixed(1)}KB)');

      // Stop any currently playing audio
      await _audioPlayer.stop();

      // Try to play the audio file with retry mechanism
      bool playbackSuccessful = false;
      int retryCount = 0;
      const maxRetries = 3;

      while (!playbackSuccessful && retryCount < maxRetries) {
        try {
          await _audioPlayer.play(DeviceFileSource(audioFile.path));
          playbackSuccessful = true;
          debugPrint('✅ Audio playback started');
        } catch (e) {
          retryCount++;
          debugPrint('❌ Audio playback failed: $e');
          if (retryCount < maxRetries) {
            await Future.delayed(Duration(milliseconds: 100 * retryCount));
          }
        }
      }

      if (!playbackSuccessful) {
        debugPrint('❌ Audio playback failed after retries');
        _modeController.add(ConversationMode.idle);
        return;
      }

      // Set mode back to idle after audio finishes
      _audioPlayer.onPlayerComplete.listen((_) {
        _modeController.add(ConversationMode.idle);
        // Clean up the temporary file after playback
        audioFile.delete().catchError((e) {
          // Silent cleanup error
          return audioFile;
        });

        // Resume recording stream
        if (_audioStreamSubscription?.isPaused ?? false) {
          _audioStreamSubscription?.resume();
        }
      });

    } catch (e) {
      debugPrint('❌ Failed to play audio: $e');
      _modeController.add(ConversationMode.idle);
      // Resume stream in case of error
      if (_audioStreamSubscription?.isPaused ?? false) {
        _audioStreamSubscription?.resume();
      }
    }
  }
  
  /// Handle WebSocket errors
  void _handleWebSocketError(error) {
    _statusController.add(ConversationStatus.error);
    _errorController.add('Connection error: $error');
    debugPrint('❌ Connection error: $error');
  }
  
  /// Handle WebSocket connection closed
  void _handleWebSocketClosed() {
    _keepAliveTimer?.cancel();
    _keepAliveTimer = null;
    
    _isConnected = false;
    _statusController.add(ConversationStatus.disconnected);
    _modeController.add(ConversationMode.idle);

    // Get close code and reason if available
    final closeCode = _webSocketChannel?.closeCode;
    final closeReason = _webSocketChannel?.closeReason;

    debugPrint('🔌 Connection closed');
    if (closeCode != null && closeCode != 1000) {
      debugPrint('❌ Close code: $closeCode');
      if (closeCode == 1006) {
        debugPrint('❌ Connection closed abnormally - possible authentication or server issue');
        _errorController.add('Connection lost unexpectedly. Please check your internet connection and try again.');
      }
    }
    if (closeReason != null && closeReason.isNotEmpty) {
      debugPrint('❌ Reason: $closeReason');
    }
    
    // Try to stop recording if still active
    if (_isRecording) {
      stopRecording().catchError((e) => debugPrint('Error stopping recording: $e'));
    }
  }
  
  /// Send text message directly (for testing)
  Future<void> sendTextMessage(String message) async {
    if (!_isConnected) {
      throw Exception('Not connected to conversation');
    }
    
    // For text messages, we simulate the user transcript
    _transcriptionController.add(message);
    debugPrint('💬 Sent text message: $message');
  }
  
  /// Convert PCM audio data to WAV format with proper headers
  Uint8List _convertPcmToWav(Uint8List pcmData) {
    const int sampleRate = ApiConstants.sampleRate; // 16000 Hz
    const int channels = ApiConstants.channels; // 1 (mono)
    const int bitsPerSample = 16;
    const int byteRate = sampleRate * channels * bitsPerSample ~/ 8;
    const int blockAlign = channels * bitsPerSample ~/ 8;

    final int dataSize = pcmData.length;
    final int fileSize = 36 + dataSize;

    final ByteData wavHeader = ByteData(44);

    // RIFF header
    wavHeader.setUint8(0, 0x52); // 'R'
    wavHeader.setUint8(1, 0x49); // 'I'
    wavHeader.setUint8(2, 0x46); // 'F'
    wavHeader.setUint8(3, 0x46); // 'F'
    wavHeader.setUint32(4, fileSize, Endian.little);
    wavHeader.setUint8(8, 0x57); // 'W'
    wavHeader.setUint8(9, 0x41); // 'A'
    wavHeader.setUint8(10, 0x56); // 'V'
    wavHeader.setUint8(11, 0x45); // 'E'

    // fmt chunk
    wavHeader.setUint8(12, 0x66); // 'f'
    wavHeader.setUint8(13, 0x6D); // 'm'
    wavHeader.setUint8(14, 0x74); // 't'
    wavHeader.setUint8(15, 0x20); // ' '
    wavHeader.setUint32(16, 16, Endian.little); // fmt chunk size
    wavHeader.setUint16(20, 1, Endian.little); // audio format (PCM)
    wavHeader.setUint16(22, channels, Endian.little);
    wavHeader.setUint32(24, sampleRate, Endian.little);
    wavHeader.setUint32(28, byteRate, Endian.little);
    wavHeader.setUint16(32, blockAlign, Endian.little);
    wavHeader.setUint16(34, bitsPerSample, Endian.little);

    // data chunk
    wavHeader.setUint8(36, 0x64); // 'd'
    wavHeader.setUint8(37, 0x61); // 'a'
    wavHeader.setUint8(38, 0x74); // 't'
    wavHeader.setUint8(39, 0x61); // 'a'
    wavHeader.setUint32(40, dataSize, Endian.little);

    // Combine header and PCM data
    final Uint8List wavFile = Uint8List(44 + dataSize);
    wavFile.setRange(0, 44, wavHeader.buffer.asUint8List());
    wavFile.setRange(44, 44 + dataSize, pcmData);

    return wavFile;
  }

  /// Configure audio player for better iOS compatibility
  Future<void> _configureAudioPlayer() async {
    try {
      // Set audio context for better iOS compatibility
      await _audioPlayer.setAudioContext(AudioContext(
        iOS: AudioContextIOS(
          category: AVAudioSessionCategory.playAndRecord,
          options: const {
            AVAudioSessionOptions.defaultToSpeaker,
            AVAudioSessionOptions.allowBluetooth,
            AVAudioSessionOptions.allowBluetoothA2DP,
          },
        ),
        android: const AudioContextAndroid(
          isSpeakerphoneOn: true,
          stayAwake: true,
          contentType: AndroidContentType.speech,
          usageType: AndroidUsageType.voiceCommunication,
          audioFocus: AndroidAudioFocus.gain,
        ),
      ));

      debugPrint('🔊 Audio configured');
    } catch (e) {
      debugPrint('⚠️ Failed to configure audio player: $e');
      // Continue initialization even if audio configuration fails
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    _keepAliveTimer?.cancel();
    _keepAliveTimer = null;
    
    await endConversation();
    await _audioRecorder.dispose();
    await _audioPlayer.dispose();

    await _statusController.close();
    await _modeController.close();
    await _transcriptionController.close();
    await _responseController.close();
    await _recordingStatusController.close();
    await _vadScoreController.close();
    await _errorController.close();

    _isInitialized = false;
    debugPrint('🧹 Service disposed');
  }
}
