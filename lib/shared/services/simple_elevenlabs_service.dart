import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../models/simple_conversation_models.dart';

class SimpleElevenLabsService extends ChangeNotifier {
  static final SimpleElevenLabsService _instance = SimpleElevenLabsService._internal();
  factory SimpleElevenLabsService() => _instance;
  SimpleElevenLabsService._internal();

  // Connection
  WebSocketChannel? _channel;
  final String _agentId = dotenv.env['ELEVENLABS_AGENT_ID'] ?? '';
  
  // Audio
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Status
  SimpleConversationStatus _status = SimpleConversationStatus.disconnected;
  SimpleConversationStatus get status => _status;
  
  String _lastTranscript = '';
  String get lastTranscript => _lastTranscript;
  
  String _lastResponse = '';
  String get lastResponse => _lastResponse;
  
  // Keep-alive timer
  Timer? _pingTimer;
  
  // Update status
  void _updateStatus(SimpleConversationStatus newStatus) {
    _status = newStatus;
    notifyListeners();
  }
  
  // Start conversation
  Future<void> startConversation() async {
    try {
      // Check microphone permission
      final status = await Permission.microphone.request();
      if (!status.isGranted) {
        _updateStatus(SimpleConversationStatus.error);
        return;
      }
      
      // Connect to WebSocket
      _updateStatus(SimpleConversationStatus.connecting);
      
      final wsUrl = 'wss://api.elevenlabs.io/v1/convai/conversation?agent_id=$_agentId';
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to messages
      _channel!.stream.listen(
        _handleMessage,
        onError: (error) {
          debugPrint('WebSocket error: $error');
          _updateStatus(SimpleConversationStatus.error);
        },
        onDone: () {
          debugPrint('WebSocket closed');
          stopConversation();
        },
      );
      
      // Send initiation message
      final initMessage = ConversationInitiation(
        conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      _sendMessage(initMessage);
      
      // Start recording
      await _startRecording();
      
      // Start keep-alive ping
      _startPingTimer();
      
      _updateStatus(SimpleConversationStatus.connected);
      
    } catch (e) {
      debugPrint('Error starting conversation: $e');
      _updateStatus(SimpleConversationStatus.error);
    }
  }
  
  // Stop conversation
  Future<void> stopConversation() async {
    _pingTimer?.cancel();
    await _stopRecording();
    await _channel?.sink.close();
    _channel = null;
    _updateStatus(SimpleConversationStatus.disconnected);
  }
  
  // Handle incoming messages
  void _handleMessage(dynamic data) {
    try {
      final json = jsonDecode(data);
      debugPrint('Received message type: ${json['type']}');
      
      final message = SimpleConversationMessage.fromJson(json);
      
      if (message is UserTranscript) {
        debugPrint('User transcript: ${message.text}');
        _lastTranscript = message.text;
        _updateStatus(SimpleConversationStatus.speaking);
        notifyListeners();
      } else if (message is AgentResponse) {
        debugPrint('Agent response: ${message.text}');
        _lastResponse = message.text;
        notifyListeners();
      } else if (message is AudioMessage) {
        debugPrint('Received audio message');
        // Play audio response
        _playAudio(message.audioBase64);
      } else if (message is PingMessage) {
        // Respond with pong
        _sendMessage(PongMessage());
      }
    } catch (e) {
      debugPrint('Error handling message: $e');
      debugPrint('Raw data: $data');
    }
  }
  
  // Send message
  void _sendMessage(SimpleConversationMessage message) {
    if (_channel != null) {
      _channel!.sink.add(jsonEncode(message.toJson()));
    }
  }
  
  // Recording stream subscription
  StreamSubscription<Uint8List>? _recordingSubscription;
  
  // Start recording
  Future<void> _startRecording() async {
    try {
      if (await _recorder.hasPermission()) {
        // Start streaming audio
        final stream = await _recorder.startStream(
          const RecordConfig(
            encoder: AudioEncoder.pcm16bits,
            sampleRate: 16000,
            numChannels: 1,
          ),
        );
        
        // Keep reference to subscription
        _recordingSubscription = stream.listen(
          (chunk) {
            final base64Audio = base64Encode(chunk);
            final message = {
              'user_audio_chunk': base64Audio,
            };
            if (_channel != null) {
              _channel!.sink.add(jsonEncode(message));
            }
          },
          onError: (error) {
            debugPrint('Recording stream error: $error');
          },
          onDone: () {
            debugPrint('Recording stream ended');
          },
        );
        
        debugPrint('Recording started successfully');
      }
    } catch (e) {
      debugPrint('Error starting recording: $e');
    }
  }
  
  // Stop recording
  Future<void> _stopRecording() async {
    try {
      await _recordingSubscription?.cancel();
      _recordingSubscription = null;
      await _recorder.stop();
      debugPrint('Recording stopped');
    } catch (e) {
      debugPrint('Error stopping recording: $e');
    }
  }
  
  // Play audio
  Future<void> _playAudio(String base64Audio) async {
    try {
      _updateStatus(SimpleConversationStatus.listening);
      
      debugPrint('Audio base64 length: ${base64Audio.length}');
      
      if (base64Audio.isEmpty) {
        debugPrint('Warning: Empty audio data received');
        return;
      }
      
      final audioBytes = base64Decode(base64Audio);
      debugPrint('Decoded audio bytes length: ${audioBytes.length}');
      
      // Add WAV header to raw PCM data
      final wavBytes = _addWavHeader(audioBytes);
      debugPrint('WAV bytes length: ${wavBytes.length}');
      
      // Write to temporary file for iOS compatibility
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/elevenlabs_audio_${DateTime.now().millisecondsSinceEpoch}.wav');
      await tempFile.writeAsBytes(wavBytes);
      debugPrint('Audio file written to: ${tempFile.path}');
      
      // Play from file
      await _audioPlayer.play(DeviceFileSource(tempFile.path));
      debugPrint('Audio playback started');
      
      // Clean up after playing
      _audioPlayer.onPlayerComplete.listen((_) {
        if (tempFile.existsSync()) {
          tempFile.deleteSync();
          debugPrint('Temp audio file cleaned up');
        }
      });
    } catch (e) {
      debugPrint('Error playing audio: $e');
      debugPrint('Stack trace: ${StackTrace.current}');
    }
  }
  
  // Add WAV header to PCM data
  Uint8List _addWavHeader(Uint8List pcmData) {
    const int sampleRate = 16000;
    const int numChannels = 1;
    const int bitsPerSample = 16;
    
    final int dataSize = pcmData.length;
    final int fileSize = dataSize + 36;
    
    final wavHeader = Uint8List(44);
    final byteData = ByteData.view(wavHeader.buffer);
    
    // RIFF header
    wavHeader.setRange(0, 4, 'RIFF'.codeUnits);
    byteData.setUint32(4, fileSize, Endian.little);
    wavHeader.setRange(8, 12, 'WAVE'.codeUnits);
    
    // fmt chunk
    wavHeader.setRange(12, 16, 'fmt '.codeUnits);
    byteData.setUint32(16, 16, Endian.little); // fmt chunk size
    byteData.setUint16(20, 1, Endian.little); // PCM format
    byteData.setUint16(22, numChannels, Endian.little);
    byteData.setUint32(24, sampleRate, Endian.little);
    byteData.setUint32(28, sampleRate * numChannels * bitsPerSample ~/ 8, Endian.little);
    byteData.setUint16(32, numChannels * bitsPerSample ~/ 8, Endian.little);
    byteData.setUint16(34, bitsPerSample, Endian.little);
    
    // data chunk
    wavHeader.setRange(36, 40, 'data'.codeUnits);
    byteData.setUint32(40, dataSize, Endian.little);
    
    // Combine header and PCM data
    return Uint8List.fromList([...wavHeader, ...pcmData]);
  }
  
  // Keep-alive ping
  void _startPingTimer() {
    _pingTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      _sendMessage(PingMessage());
    });
  }
  
  @override
  void dispose() {
    stopConversation();
    _recorder.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }
}