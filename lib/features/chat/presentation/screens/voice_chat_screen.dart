import 'dart:async';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../shared/services/elevenlabs_conversational_service.dart';
import '../../../../shared/models/conversation_models.dart';

class VoiceChatScreen extends StatefulWidget {
  const VoiceChatScreen({super.key});

  @override
  State<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends State<VoiceChatScreen> {
  final ElevenLabsConversationalService _service = ElevenLabsConversationalService.instance;
  
  // Simple state management
  bool _isConnected = false;
  bool _isConversationActive = false;
  String _status = 'Ready to start conversation';
  ConversationMode _mode = ConversationMode.idle;
  final List<ChatMessage> _messages = [];
  
  // Stream subscriptions
  late List<StreamSubscription> _subscriptions;

  @override
  void initState() {
    super.initState();
    _subscriptions = [];
    _setupListeners();
  }

  void _setupListeners() {
    _subscriptions.addAll([
      // Connection status
      _service.statusStream.listen((status) {
        if (mounted) {
          setState(() {
            _isConnected = status == ConversationStatus.connected;
            switch (status) {
              case ConversationStatus.connecting:
                _status = 'Connecting to Nathan...';
                break;
                             case ConversationStatus.connected:
                 _status = 'Connected! Starting conversation...';
                 // Recording will start automatically when connection is established
                 break;
              case ConversationStatus.disconnected:
                _status = 'Ready to start conversation';
                _isConversationActive = false;
                break;
              case ConversationStatus.error:
                _status = 'Connection failed. Try again.';
                _isConversationActive = false;
                break;
            }
          });
        }
      }),

      // Conversation mode
      _service.modeStream.listen((mode) {
        if (mounted) {
          setState(() {
            _mode = mode;
            switch (mode) {
              case ConversationMode.listening:
                _status = '🎤 Listening... speak naturally';
                break;
              case ConversationMode.speaking:
                _status = '🔊 Nathan is speaking...';
                break;
              case ConversationMode.processing:
                _status = '⚡ Processing...';
                break;
              case ConversationMode.idle:
                if (_isConnected && _isConversationActive) {
                  _status = '💬 Ready for conversation';
                } else if (_isConnected) {
                  _status = 'Connected! Ready to start';
                }
                break;
            }
          });
        }
      }),

      // User messages
      _service.transcriptionStream.listen((text) {
        if (mounted) {
          setState(() {
            _messages.add(ChatMessage(
              text: text,
              isUser: true,
              timestamp: DateTime.now(),
            ));
          });
        }
      }),

      // Nathan's responses
      _service.responseStream.listen((text) {
        if (mounted) {
          setState(() {
            _messages.add(ChatMessage(
              text: text,
              isUser: false,
              timestamp: DateTime.now(),
            ));
          });
        }
      }),

      // Errors
      _service.errorStream.listen((error) {
        if (mounted) {
          setState(() {
            _status = 'Error: $error';
            _isConversationActive = false;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $error'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }),
    ]);
  }

  Future<void> _startConversation() async {
    try {
      setState(() {
        _status = 'Checking permissions...';
      });

      // Check microphone permission
      final permission = await Permission.microphone.status;
      if (permission != PermissionStatus.granted) {
        final result = await Permission.microphone.request();
        if (result != PermissionStatus.granted) {
          _showPermissionDialog();
          return;
        }
      }

      setState(() {
        _status = 'Starting conversation...';
        _isConversationActive = true;
      });

             // Initialize and start conversation
       await _service.initialize();
       await _service.startConversation();

    } catch (e) {
      setState(() {
        _status = 'Failed to start: $e';
        _isConversationActive = false;
      });
    }
  }

  Future<void> _stopConversation() async {
    try {
      setState(() {
        _status = 'Stopping conversation...';
        _isConversationActive = false;
      });

      await _service.endConversation();

    } catch (e) {
      setState(() {
        _status = 'Error stopping: $e';
      });
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Microphone Permission Required'),
        content: const Text(
          'To talk with Nathan, please:\n\n'
          '1. Tap "Open Settings" below\n'
          '2. Find "Fitness App" in the list\n'
          '3. Turn on the Microphone toggle\n'
          '4. Return to the app\n\n'
          'This permission is needed to enable voice conversations with your AI fitness coach.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await openAppSettings();
            },
            child: const Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _startConversation();
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    switch (_mode) {
      case ConversationMode.listening:
        return Colors.red;
      case ConversationMode.speaking:
        return Colors.green;
      case ConversationMode.processing:
        return Colors.blue;
      case ConversationMode.idle:
        return _isConversationActive ? Colors.orange : Colors.grey;
    }
  }

  @override
  void dispose() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    _service.endConversation();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: const Text(
          'Nathan - AI Coach',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF1A1A1A),
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Chat messages area
              Expanded(
                flex: 3,
                child: _messages.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 64,
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Start a conversation with Nathan!',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.6),
                                fontSize: 18,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'He\'ll listen and respond automatically',
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.4),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: _messages.length,
                        itemBuilder: (context, index) {
                          final message = _messages[index];
                          return ChatBubble(message: message);
                        },
                      ),
              ),

              // Status and controls area
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Status text
                    Text(
                      _status,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Visual indicator
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getStatusColor(),
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColor().withValues(alpha: 0.4),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        _getStatusIcon(),
                        color: Colors.white,
                        size: 48,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Main action button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: _isConversationActive ? _stopConversation : _startConversation,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isConversationActive ? Colors.red : Colors.orange,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                        ),
                        child: Text(
                          _isConversationActive ? 'Stop Conversation' : 'Start Conversation',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    if (_isConversationActive) ...[
                      const SizedBox(height: 16),
                      Text(
                        'Conversation is running automatically.\nJust speak naturally!',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.6),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon() {
    switch (_mode) {
      case ConversationMode.listening:
        return Icons.mic;
      case ConversationMode.speaking:
        return Icons.volume_up;
      case ConversationMode.processing:
        return Icons.sync;
      case ConversationMode.idle:
        return _isConversationActive ? Icons.chat : Icons.mic_off;
    }
  }
}

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            const CircleAvatar(
              backgroundColor: Colors.orange,
              radius: 16,
              child: Text('N', style: TextStyle(color: Colors.white)),
            ),
            const SizedBox(width: 8),
          ],
          
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: message.isUser ? Colors.blue : Colors.grey[800],
                borderRadius: BorderRadius.circular(18),
              ),
              child: Text(
                message.text,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 15,
                ),
              ),
            ),
          ),
          
          if (message.isUser) ...[
            const SizedBox(width: 8),
            const CircleAvatar(
              backgroundColor: Colors.blue,
              radius: 16,
              child: Text('U', style: TextStyle(color: Colors.white)),
            ),
          ],
        ],
      ),
    );
  }
}
